"use client";

import { useRef, useState, useEffect } from 'react';
import ProjectCard from './ProjectCard';
import ProjectText from './ProjectText';
import AnimatedFixedTitle from './AnimatedFixedTitle';

const Projects = () => {
  const sectionRef = useRef(null);
  const [hasAnimatedIn, setHasAnimatedIn] = useState(false);
  const [titleVisible, setTitleVisible] = useState(false);
  const [scrollEffects, setScrollEffects] = useState({
    opacity: 1,
    blur: 0,
    scale: 1
  });
  const [projectScrollProgress, setProjectScrollProgress] = useState(0);
  const [isProjectsComplete, setIsProjectsComplete] = useState(false);
  const [normalScrollProgress, setNormalScrollProgress] = useState(0);
  
  // Projects data array
  const projects = [
    {
      id: 1,
      title: "Portfolio Website",
      description: "A modern, responsive portfolio website built with Next.js and Framer Motion. Features smooth animations, scroll-based interactions, and a clean design system.",
      techStack: ['Next.js', 'React', 'Tailwind CSS', 'Framer Motion'],
      label: "Featured Project"
    },
    {
      id: 2,
      title: "E-Commerce Platform",
      description: "Full-stack e-commerce solution with user authentication, payment processing, and admin dashboard. Built for scalability and performance.",
      techStack: ['React', 'Node.js', 'MongoDB', 'Stripe'],
      label: "Full-Stack Project"
    },
    {
      id: 3,
      title: "Task Management App",
      description: "Collaborative task management application with real-time updates, team collaboration features, and intuitive drag-and-drop interface.",
      techStack: ['Vue.js', 'Firebase', 'Vuetify', 'Socket.io'],
      label: "Collaborative App"
    },
    {
      id: 4,
      title: "Weather Dashboard",
      description: "Interactive weather dashboard with location-based forecasts, historical data visualization, and customizable widgets for weather tracking.",
      techStack: ['React', 'D3.js', 'OpenWeather API', 'Chart.js'],
      label: "Data Visualization"
    }
  ];
  
  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;

      // Find the Services section by looking for it in the DOM
      const servicesSection = document.querySelector('[class*="bg-primary py-16"]');
      if (!servicesSection) return;

      const servicesRect = servicesSection.getBoundingClientRect();
      const projectsRect = sectionRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Calculate when Services section is mostly scrolled past
      const servicesBottom = servicesRect.bottom;
      const servicesTop = servicesRect.top;

      // Show title earlier - adjust this value to control when title disappears on scroll up
      // Higher values = disappears earlier, Lower values = disappears later
      const triggerPoint = windowHeight * 0.3; // Try 0.6 (60%) to make it disappear earlier
      const servicesAlmostGone = servicesBottom <= triggerPoint;
      const servicesStartedScrolling = servicesTop <= windowHeight * 0.8;

      // Title appears when Services is almost gone - SAME LOGIC AS SERVICES
      const shouldShowTitle = servicesAlmostGone && servicesStartedScrolling;

      // Show animation when scrolling down and condition is met
      if (shouldShowTitle && !hasAnimatedIn) {
        setHasAnimatedIn(true);
        setTitleVisible(true);
      }

      // Hide animation when scrolling back up past the same trigger point
      if (!shouldShowTitle && hasAnimatedIn) {
        setHasAnimatedIn(false);
        setTitleVisible(false);
      }

      // Calculate scroll-driven effects when title is visible
      if (titleVisible) {
        // Calculate how far we've scrolled into the Projects section
        const projectsTop = projectsRect.top;
        const projectsHeight = projectsRect.height;

        // Start project animations when Projects section reaches top of viewport
        const triggerOffset = windowHeight * 0.1;
        if (projectsTop <= triggerOffset) {
          // Calculate scroll progress through Projects section with three phases:
          // Phase 1: Project animations (0-75% of section height) - SLOW & SMOOTH
          // Phase 2: Settling period (75-80% of section height) - PAUSE
          // Phase 3: Normal scroll transition (80-100% = exactly 100vh) - NATURAL SPEED
          const scrolledIntoProjects = Math.abs(projectsTop - triggerOffset);

          const projectAnimationDistance = projectsHeight * 0.75; // 75% for smooth animations (300vh)
          const settlingDistance = projectsHeight * 0.05;         // 5% for settling (20vh)
          const normalScrollDistance = projectsHeight * 0.2;      // 20% for normal scroll (80vh)
          const totalAnimationDistance = projectAnimationDistance + settlingDistance;

          const rawScrollProgress = Math.min(1, scrolledIntoProjects / projectAnimationDistance);
          const settlingProgress = Math.min(1, Math.max(0, (scrolledIntoProjects - projectAnimationDistance) / settlingDistance));

          // Projects animation is complete after settling period finishes
          const projectsAnimationComplete = settlingProgress >= 1.0;

          if (!projectsAnimationComplete) {
            // Phase 1: Project animations (0% - 60% of section height)
            setIsProjectsComplete(false);
            setNormalScrollProgress(0);

            // Add a delay buffer - title stays crisp for the first 15% of scroll progress
            const delayBuffer = 0.10;
            const adjustedScrollProgress = Math.max(0, (rawScrollProgress - delayBuffer) / (1 - delayBuffer));

            // Apply scroll-driven effects to title with smoother transitions (only after delay)
            const opacity = adjustedScrollProgress > 0 ? Math.max(0.05, 1 - (adjustedScrollProgress * 4)) : 1;
            const blur = adjustedScrollProgress * 15;
            const scale = adjustedScrollProgress > 0 ? Math.max(0.8, 1 - (adjustedScrollProgress * 0.5)) : 1;

            setScrollEffects({ opacity, blur, scale });
            setProjectScrollProgress(rawScrollProgress);
          } else {
            // Phase 3: Projects complete (after settling), calculate normal scroll progress
            setIsProjectsComplete(true);
            setProjectScrollProgress(1); // Keep projects at final state

            // Calculate normal scroll progress for the remaining 20% of section height (80vh)
            const normalScrollStart = totalAnimationDistance; // Start after settling period
            const normalScrollAmount = Math.max(0, scrolledIntoProjects - normalScrollStart);
            const normalProgress = Math.min(1, normalScrollAmount / normalScrollDistance);

            setNormalScrollProgress(normalProgress);

            // Keep title effects at final state during normal scroll
            setScrollEffects({ opacity: 0.05, blur: 15, scale: 0.8 });
          }
        } else {
          // Reset effects when Projects section hasn't reached the trigger point yet
          setScrollEffects({ opacity: 1, blur: 0, scale: 1 });
          setProjectScrollProgress(0);
          setIsProjectsComplete(false);
          setNormalScrollProgress(0);
        }
      } else {
        // Title not visible yet - reset everything
        setScrollEffects({ opacity: 1, blur: 0, scale: 1 });
        setProjectScrollProgress(0);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    // Check initial position
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasAnimatedIn, titleVisible]);
  
  // Animation variants moved to AnimatedFixedTitle component

  return (
    <>
      {/* Fixed/Centered Title with scroll-driven effects - LOWER Z-INDEX */}
      <div
        className="fixed inset-0 flex items-center justify-center pointer-events-none z-5"
        style={{
          transform: isProjectsComplete ? `translateY(${-normalScrollProgress * 100}vh)` : 'translateY(0)'
        }}
      >
        <AnimatedFixedTitle
          title="Projects"
          titleVisible={titleVisible}
          scrollEffects={scrollEffects}
          className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl"
          containerClassName=""
        />
      </div>

      {/* Project Text - Fixed position within centered container */}
      <div
        className="fixed inset-0 flex items-center justify-center pointer-events-none z-10"
        style={{
          opacity: projectScrollProgress > 0 ? 1 : 0,
          transform: isProjectsComplete ? `translateY(${-normalScrollProgress * 100}vh)` : 'translateY(0)'
        }}
      >
        {/* Main container - adjust w-9/10 to change overall width */}
        <div className="w-9/10 h-screen flex pointer-events-none">
          {/* Project Text - Left Side (1/3 of container) */}
          <div className="w-1/3 h-full flex items-center pl-8 lg:pl-12 bg-primary pointer-events-auto">
            <div className="w-full">
              <ProjectText scrollProgress={projectScrollProgress} projects={projects} />
            </div>
          </div>

          {/* Spacer for card area - no overflow hidden here */}
          <div className="w-2/3 h-full"></div>
        </div>
      </div>

      {/* Project Card - Full screen positioning to allow sliding from outside */}
      <div
        className="fixed inset-0 flex items-center justify-center pointer-events-none z-10"
        style={{
          opacity: projectScrollProgress > 0 ? 1 : 0,
          transform: isProjectsComplete ? `translateY(${-normalScrollProgress * 100}vh)` : 'translateY(0)'
        }}
      >
        {/* Container that matches the text container positioning */}
        <div className="w-9/10 h-screen flex pointer-events-none">
          {/* Spacer to align with text layout */}
          <div className="w-1/3 h-full"></div>

          {/* Project Card Area - 2/3 of container, but cards can slide from outside */}
          <div className="w-2/3 h-full flex items-center pr-8 lg:pr-12 pointer-events-auto relative">
            <div className="w-full h-full bg-background">
              <ProjectCard scrollProgress={projectScrollProgress} projects={projects} />
            </div>
          </div>
        </div>
      </div>

      {/* Projects Section - provides scroll space */}
      <section
        ref={sectionRef}
        data-section="projects"
        className="bg-background py-20 min-h-[400vh]"
      >
        <div className="w-full mx-auto px-6">
          {/* Invisible spacer to provide scroll area */}
          <div className="h-[400vh]"></div>
        </div>
      </section>
    </>
  );
};

export default Projects;
