"use client";

import { motion } from 'framer-motion';

const ProcessCard = ({ scrollProgress, processSteps }) => {
  // Calculate which step we're currently showing
  const stepCount = processSteps.length;

  // For now, with just one step, show it when scroll progress starts
  const currentStep = processSteps[0];

  // Calculate card position based on scroll progress - START IMMEDIATELY LIKE PROJECT CARDS
  // Cards should start appearing as soon as ANY scroll progress begins (even 0.01)
  // Start from just below viewport (75vh) and move to TRUE CENTER (0vh = middle of screen)
  const startPositionVh = 75;  // Start closer to viewport
  const endPositionVh = 0;     // End at TRUE center of viewport (same as Process title)

  // Card appears immediately when ANY scroll progress starts (like project cards)
  // Use natural scroll speed - direct mapping to scroll progress
  const cardProgress = scrollProgress; // Natural scroll speed - no multipliers
  const cardPositionVh = startPositionVh - (cardProgress * (startPositionVh - endPositionVh));

  // Opacity appears immediately when ANY scroll starts
  const cardOpacity = scrollProgress > 0 ? 1 : 0;

  return (
    <div className="w-full h-full relative flex items-center justify-center">
      {/* Process Card - NO ANIMATION, NATURAL SCROLL SPEED */}
      <div
        className="absolute bg-primary rounded-2xl border border-secondary/20 shadow-lg p-8"
        style={{
          opacity: cardOpacity,
          transform: `translateY(${cardPositionVh}vh)`,
          // Responsive sizing based on viewport - smaller than project cards
          width: 'min(300px, 25vw)', // Responsive width, max 300px
          height: 'min(200px, 20vh)', // Responsive height, max 200px
          zIndex: 1,
          // NO TRANSITION - natural scroll speed only
          transition: 'none'
        }}
      >
        {/* Card Content */}
        <div className="h-full flex flex-col justify-center text-center">
          {/* Step Number */}
          <div className="text-secondary text-4xl font-bold mb-2">
            {currentStep.number}
          </div>
          
          {/* Step Title */}
          <h3 className="text-secondary text-lg font-semibold mb-3">
            {currentStep.title}
          </h3>
          
          {/* Step Description */}
          <p className="text-secondary text-sm leading-relaxed">
            {currentStep.description}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProcessCard;
